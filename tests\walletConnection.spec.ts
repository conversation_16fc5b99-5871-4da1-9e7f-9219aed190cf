import { test } from '../fixtures/pageFixtures';
import { HeaderXPath } from '../utils';

test.describe('Login Tests', () => {
  test('should login with Google account', async ({ page, context }) => {
    // Step 1: T<PERSON>y cập website
    await page.goto('https://lamboo.finance/');
    await page.waitForLoadState('networkidle');

    // Step 2: Click button login
    const loginButton = page.locator(HeaderXPath.loginButton);
    await loginButton.click();

    // Chờ popup login hiện ra
    await page.waitForTimeout(2000);

    // Step 3: Click biểu tượng Google trên popup login và handle new window
    const googleButton = page.locator(HeaderXPath.google);

    // Listen for new page/window
    const pagePromise = context.waitForEvent('page');
    await googleButton.click();

    // Get the new Google login window
    const googlePage = await pagePromise;
    await googlePage.waitForLoadState('networkidle');

    console.log('Google login window opened:', googlePage.url());

    // Step 4: Điền email Google trong window mới
    const emailInput = googlePage.locator(HeaderXPath.email);
    await emailInput.fill('<EMAIL>');

    // Step 5: Click Next sau khi điền email
    const nextEmailButton = googlePage.locator(HeaderXPath.nextEmail);
    await nextEmailButton.click();

    // Chờ trang password load
    await googlePage.waitForTimeout(3000);

    // Step 6: Điền password
    const passwordInput = googlePage.locator(HeaderXPath.password);
    await passwordInput.fill('Dex3123456@@');

    // Step 7: Click Next sau khi điền password
    const nextPasswordButton = googlePage.locator(HeaderXPath.nextPassword);
    await nextPasswordButton.click();

    // Chờ để Google login hoàn thành và window đóng
    await googlePage.waitForTimeout(5000);

    // Quay lại trang chính để kiểm tra login thành công
    await page.bringToFront();
    await page.waitForTimeout(3000);

    console.log('Login process completed');
  });

  test('should access website and click login button only', async ({ page }) => {
    // Step 1: Truy cập website
    await page.goto('https://lamboo.finance/');
    await page.waitForLoadState('networkidle');

    // Step 2: Click button login
    const loginButton = page.locator(HeaderXPath.loginButton);
    await loginButton.click();

    // Chờ một chút để xem popup login
    await page.waitForTimeout(3000);
  });

  test('should click Google login button and handle new window', async ({ page, context }) => {
    // Step 1: Truy cập website
    await page.goto('https://lamboo.finance/');
    await page.waitForLoadState('networkidle');

    // Step 2: Click button login
    const loginButton = page.locator(HeaderXPath.loginButton);
    await loginButton.click();
    await page.waitForTimeout(2000);

    // Step 3: Click biểu tượng Google và handle new window
    const googleButton = page.locator(HeaderXPath.google);

    // Listen for new page/window
    const pagePromise = context.waitForEvent('page');
    await googleButton.click();

    // Get the new Google login window
    const googlePage = await pagePromise;
    await googlePage.waitForLoadState('networkidle');

    console.log('Google login window URL:', googlePage.url());
    console.log('Google login window title:', await googlePage.title());

    // Chờ để xem Google login page
    await googlePage.waitForTimeout(5000);

    // Đóng Google window
    await googlePage.close();
  });

  test('should handle Google login with error handling', async ({ page, context }) => {
    // Step 1: Truy cập website
    await page.goto('https://lamboo.finance/');
    await page.waitForLoadState('networkidle');

    // Step 2: Click button login
    const loginButton = page.locator(HeaderXPath.loginButton);
    await loginButton.click();
    await page.waitForTimeout(2000);

    // Step 3: Click Google button với error handling
    const googleButton = page.locator(HeaderXPath.google);

    try {
      // Listen for new page với timeout
      const pagePromise = context.waitForEvent('page', { timeout: 10000 });
      await googleButton.click();

      const googlePage = await pagePromise;
      await googlePage.waitForLoadState('networkidle');

      console.log('✅ Google window opened successfully');
      console.log('URL:', googlePage.url());

      // Test điền email
      const emailInput = googlePage.locator(HeaderXPath.email);
      if (await emailInput.isVisible()) {
        await emailInput.fill('<EMAIL>');
        console.log('✅ Email filled successfully');
      }

      await googlePage.waitForTimeout(3000);
      await googlePage.close();

    } catch (error) {
      console.log('❌ Error handling Google login:', error);
    }
  });
});
