export const HeaderXPath = {
  logo: "//a[@href='/top-tokens' and .//svg]",
  markets: "//a[@href='/top-tokens' and normalize-space()='Markets']",
  portfolio: "//a[contains(@href,'/portfolio') and normalize-space()='Portfolio']",
  copyTrading: "//a[@href='/copy-trading' and normalize-space()='Copy Trading']",
  perpetuals: "//a[@href='/perpetuals' and normalize-space()='Perpetuals']",
  searchButton: "//button[.//p[normalize-space()='Search by token or CA']]",
  loginButton: "//button[normalize-space()='Login']",
  depositButton: "//button[normalize-space()='Deposit']",
  starIconButton: "//button[normalize-space()='Deposit']/following-sibling::button[1]",
  bellButton: "//div[.//button[normalize-space()='Deposit']]//button[@aria-haspopup='menu'][1]",
  settingsButton: "//a[@href='/settings']",
  walletButton: "//a[@href='/settings']/following::button[@aria-haspopup='menu'][1]",
  google: "//img[contains(@src,'login-google')]/ancestor::button[1]",
  x: "//img[contains(@src,'login-X')]/ancestor::button[1]",
  apple: "//img[contains(@src,'login-apple')]/ancestor::button[1]",
  email: "//*[@id='identifierId']",
  password: "//input[@name='Passwd' and @type='password']",
  nextEmail: "//*[@id='identifierNext']",
  nextPassword: "//*[@id='passwordNext']",
};
