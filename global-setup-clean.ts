import { FullConfig } from '@playwright/test';
import * as fs from 'fs';

/**
 * Global setup to clean old test results before running tests
 */
async function globalSetupClean(config: FullConfig) {
  console.log('🧹 Cleaning old test results...');

  // Function to clean old reports
  function cleanOldReports() {
    const dirsToClean = ['test-results', 'playwright-report', 'temp-results'];
    
    dirsToClean.forEach(dir => {
      if (fs.existsSync(dir)) {
        try {
          fs.rmSync(dir, { recursive: true, force: true });
          console.log(`✅ Cleaned: ${dir}`);
        } catch (error: any) {
          console.warn(`⚠️ Could not clean ${dir}:`, error?.message || error);
          
          // Try alternative method for Windows/OneDrive
          try {
            if (process.platform === 'win32') {
              const { execSync } = require('child_process');
              execSync(`rmdir /s /q "${dir}"`, { stdio: 'ignore' });
              console.log(`✅ Force cleaned: ${dir}`);
            }
          } catch (altError) {
            console.warn(`⚠️ Alternative clean also failed for ${dir}`);
          }
        }
      }
    });
  }

  // Clean reports
  cleanOldReports();
  
  console.log('✅ Cleanup completed');
}

export default globalSetupClean;
