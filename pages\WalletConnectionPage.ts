import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';

/**
 * Wallet Connection Page Object Model for Lamboo Finance
 * Handles wallet connection functionality (equivalent to login for DeFi apps)
 */
export class WalletConnectionPage extends BasePage {
  // Connect wallet button and states
  readonly connectWalletButton: Locator;
  readonly connectWalletModal: Locator;
  readonly walletConnectedState: Locator;
  readonly disconnectButton: Locator;
  readonly userAccountInfo: Locator;

  // Wallet selection modal elements
  readonly walletModal: Locator;
  readonly walletModalTitle: Locator;
  readonly walletModalCloseButton: Locator;
  readonly walletOptions: Locator;

  // Popular wallet options
  readonly metamaskOption: Locator;
  readonly walletConnectOption: Locator;
  readonly coinbaseWalletOption: Locator;
  readonly trustWalletOption: Locator;
  readonly phantomWalletOption: Locator;

  // Connection states and messages
  readonly connectingMessage: Locator;
  readonly connectionSuccessMessage: Locator;
  readonly connectionErrorMessage: Locator;
  readonly networkSwitchMessage: Locator;

  // User account display elements
  readonly walletAddress: Locator;
  readonly accountBalance: Locator;
  readonly networkIndicator: Locator;
  readonly userDropdownMenu: Locator;

  // Loading and error states
  readonly loadingSpinner: Locator;
  readonly errorAlert: Locator;
  readonly retryButton: Locator;

  constructor(page: Page) {
    super(page, '/');
    
    // Connect wallet button and states
    this.connectWalletButton = page.locator('button:has-text("Connect"), button:has-text("Connect Wallet"), [data-testid="connect-wallet"]').first();
    this.connectWalletModal = page.locator('.wallet-modal, [data-testid="wallet-modal"], [role="dialog"]').first();
    this.walletConnectedState = page.locator('.wallet-connected, [data-testid="wallet-connected"]').first();
    this.disconnectButton = page.locator('button:has-text("Disconnect"), [data-testid="disconnect-wallet"]').first();
    this.userAccountInfo = page.locator('.account-info, [data-testid="account-info"]').first();

    // Wallet selection modal
    this.walletModal = page.locator('.wallet-selection-modal, [data-testid="wallet-selection"]').first();
    this.walletModalTitle = page.locator('.modal-title, h2, h3').filter({ hasText: /wallet|connect/i }).first();
    this.walletModalCloseButton = page.locator('button[aria-label="Close"], .close-button, [data-testid="close-modal"]').first();
    this.walletOptions = page.locator('.wallet-option, [data-testid="wallet-option"]');

    // Wallet options
    this.metamaskOption = page.locator('button:has-text("MetaMask"), [data-testid="metamask-option"]').first();
    this.walletConnectOption = page.locator('button:has-text("WalletConnect"), [data-testid="walletconnect-option"]').first();
    this.coinbaseWalletOption = page.locator('button:has-text("Coinbase"), [data-testid="coinbase-option"]').first();
    this.trustWalletOption = page.locator('button:has-text("Trust"), [data-testid="trust-option"]').first();
    this.phantomWalletOption = page.locator('button:has-text("Phantom"), [data-testid="phantom-option"]').first();

    // Connection states
    this.connectingMessage = page.locator('text="Connecting", text="Please wait", .connecting-message').first();
    this.connectionSuccessMessage = page.locator('text="Connected", text="Success", .success-message').first();
    this.connectionErrorMessage = page.locator('text="Failed", text="Error", .error-message').first();
    this.networkSwitchMessage = page.locator('text="Switch Network", text="Wrong Network"').first();

    // User account elements
    this.walletAddress = page.locator('.wallet-address, [data-testid="wallet-address"]').first();
    this.accountBalance = page.locator('.account-balance, [data-testid="account-balance"]').first();
    this.networkIndicator = page.locator('.network-indicator, [data-testid="network-indicator"]').first();
    this.userDropdownMenu = page.locator('.user-menu, [data-testid="user-menu"]').first();

    // Loading and error states
    this.loadingSpinner = page.locator('.loading, .spinner, [data-testid="loading"]').first();
    this.errorAlert = page.locator('.error-alert, [role="alert"]').first();
    this.retryButton = page.locator('button:has-text("Retry"), [data-testid="retry-button"]').first();
  }

  /**
   * Navigate to home page and prepare for wallet connection
   */
  async navigateToWalletConnection(): Promise<void> {
    await this.goto();
    await this.waitForPageLoad();
    await this.waitForWalletElements();
  }

  /**
   * Wait for wallet-related elements to be ready
   */
  async waitForWalletElements(): Promise<void> {
    // Wait for loading to complete
    if (await this.loadingSpinner.isVisible()) {
      await this.waitForElementToBeHidden(this.loadingSpinner);
    }
    
    // Wait for connect wallet button or connected state
    try {
      await this.waitForElement(this.connectWalletButton, 10000);
    } catch {
      // If connect button not found, check if already connected
      await this.waitForElement(this.walletConnectedState, 5000);
    }
  }

  /**
   * Click connect wallet button to open wallet selection modal
   */
  async clickConnectWallet(): Promise<void> {
    await this.waitForElement(this.connectWalletButton);
    await this.clickWithRetry(this.connectWalletButton);
    
    // Wait for wallet modal to appear
    await this.waitForElement(this.walletModal, 10000);
  }

  /**
   * Select a specific wallet option
   */
  async selectWallet(walletName: string): Promise<void> {
    await this.waitForElement(this.walletModal);
    
    let walletOption: Locator;
    
    switch (walletName.toLowerCase()) {
      case 'metamask':
        walletOption = this.metamaskOption;
        break;
      case 'walletconnect':
        walletOption = this.walletConnectOption;
        break;
      case 'coinbase':
        walletOption = this.coinbaseWalletOption;
        break;
      case 'trust':
        walletOption = this.trustWalletOption;
        break;
      case 'phantom':
        walletOption = this.phantomWalletOption;
        break;
      default:
        // Try to find wallet by text
        walletOption = this.walletOptions.filter({ hasText: new RegExp(walletName, 'i') }).first();
    }

    await this.waitForElement(walletOption);
    await this.clickWithRetry(walletOption);
  }

  /**
   * Complete wallet connection process
   */
  async connectWallet(walletName: string = 'metamask'): Promise<void> {
    // Click connect wallet button
    await this.clickConnectWallet();
    
    // Select wallet
    await this.selectWallet(walletName);
    
    // Wait for connection process
    await this.waitForConnectionResult();
  }

  /**
   * Wait for connection result (success or error)
   */
  async waitForConnectionResult(timeout: number = 30000): Promise<void> {
    // Wait for either success or error state
    try {
      // Check for connecting message first
      if (await this.connectingMessage.isVisible()) {
        await this.waitForElementToBeHidden(this.connectingMessage, timeout);
      }

      // Wait for either success or error
      await Promise.race([
        this.waitForElement(this.walletConnectedState, timeout),
        this.waitForElement(this.connectionSuccessMessage, timeout),
        this.waitForElement(this.connectionErrorMessage, timeout)
      ]);
    } catch (error) {
      console.warn('Connection result timeout or error:', error);
    }
  }

  /**
   * Check if wallet is connected
   */
  async isWalletConnected(): Promise<boolean> {
    // Check multiple indicators of connected state
    const indicators = [
      this.walletConnectedState,
      this.userAccountInfo,
      this.walletAddress,
      this.disconnectButton
    ];

    for (const indicator of indicators) {
      if (await indicator.isVisible()) {
        return true;
      }
    }

    return false;
  }

  /**
   * Get connected wallet address
   */
  async getWalletAddress(): Promise<string | null> {
    if (await this.walletAddress.isVisible()) {
      const address = await this.getElementText(this.walletAddress);
      return address.trim();
    }
    return null;
  }

  /**
   * Get account balance if displayed
   */
  async getAccountBalance(): Promise<string | null> {
    if (await this.accountBalance.isVisible()) {
      const balance = await this.getElementText(this.accountBalance);
      return balance.trim();
    }
    return null;
  }

  /**
   * Disconnect wallet
   */
  async disconnectWallet(): Promise<void> {
    // Try different ways to access disconnect option
    if (await this.disconnectButton.isVisible()) {
      await this.clickWithRetry(this.disconnectButton);
    } else if (await this.userDropdownMenu.isVisible()) {
      // Click user menu to open dropdown
      await this.clickWithRetry(this.userDropdownMenu);
      
      // Look for disconnect option in dropdown
      const disconnectOption = this.page.locator('button:has-text("Disconnect"), a:has-text("Disconnect")').first();
      await this.waitForElement(disconnectOption);
      await this.clickWithRetry(disconnectOption);
    }

    // Wait for disconnection to complete
    await this.waitForElementToBeHidden(this.walletConnectedState, 10000);
  }

  /**
   * Close wallet modal
   */
  async closeWalletModal(): Promise<void> {
    if (await this.walletModal.isVisible()) {
      if (await this.walletModalCloseButton.isVisible()) {
        await this.clickWithRetry(this.walletModalCloseButton);
      } else {
        // Try pressing Escape key
        await this.page.keyboard.press('Escape');
      }
      
      await this.waitForElementToBeHidden(this.walletModal);
    }
  }

  /**
   * Check if connection error occurred
   */
  async hasConnectionError(): Promise<boolean> {
    return await this.isElementVisible(this.connectionErrorMessage) || 
           await this.isElementVisible(this.errorAlert);
  }

  /**
   * Get connection error message
   */
  async getConnectionErrorMessage(): Promise<string | null> {
    if (await this.connectionErrorMessage.isVisible()) {
      return await this.getElementText(this.connectionErrorMessage);
    }
    if (await this.errorAlert.isVisible()) {
      return await this.getElementText(this.errorAlert);
    }
    return null;
  }

  /**
   * Retry connection after error
   */
  async retryConnection(): Promise<void> {
    if (await this.retryButton.isVisible()) {
      await this.clickWithRetry(this.retryButton);
    } else {
      // Try connecting again from the beginning
      await this.clickConnectWallet();
    }
  }

  /**
   * Get available wallet options
   */
  async getAvailableWallets(): Promise<string[]> {
    await this.waitForElement(this.walletModal);
    const options = await this.walletOptions.all();
    const walletNames: string[] = [];

    for (const option of options) {
      const text = await option.textContent();
      if (text) {
        walletNames.push(text.trim());
      }
    }

    return walletNames;
  }

  /**
   * Check if specific wallet option is available
   */
  async isWalletOptionAvailable(walletName: string): Promise<boolean> {
    await this.waitForElement(this.walletModal);
    const option = this.walletOptions.filter({ hasText: new RegExp(walletName, 'i') }).first();
    return await option.isVisible();
  }

  /**
   * Simulate wallet connection without actual wallet
   * (For testing purposes when no real wallet is available)
   */
  async simulateWalletConnection(): Promise<void> {
    // This method can be used to mock wallet connection for testing
    // Implementation would depend on the specific testing needs
    console.log('Simulating wallet connection for testing purposes');
    
    // Click connect wallet
    await this.clickConnectWallet();
    
    // Take screenshot of wallet modal
    await this.takeScreenshot('wallet-modal-opened');
    
    // Close modal for now
    await this.closeWalletModal();
  }
}
